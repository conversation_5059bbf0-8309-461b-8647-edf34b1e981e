<script setup lang="ts">
import { useTheme, type Theme } from '~/composables/useTheme';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';

const { theme, setTheme } = useTheme();

const themes = [
    { key: 'light', label: 'Light', icon: 'tabler:sun' },
    { key: 'dark', label: 'Dark', icon: 'tabler:moon' },
    { key: 'system', label: 'System', icon: 'tabler:device-desktop' },
];

const currentTheme = computed(() => {
    return themes.find(t => t.key === theme.value) || themes[2];
});
</script>

<template>
    <Menu
        as="div"
        class="relative inline-block text-left"
    >
        <div>
            <MenuButton
                class="inline-flex w-8 h-8 justify-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
            >
                <Icon
                    :name="currentTheme?.icon || 'tabler:device-desktop'"
                    class="h-5 w-5"
                />
                <span class="sr-only">{{ currentTheme?.label || 'System' }} theme</span>
            </MenuButton>
        </div>

        <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
        >
            <MenuItems
                class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-600 focus:outline-none"
            >
                <div class="py-1">
                    <MenuItem
                        v-for="themeOption in themes"
                        :key="themeOption.key"
                        v-slot="{ active }"
                    >
                        <button
                            :class="[
                                active ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300',
                                theme === themeOption.key ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : '',
                                'group flex w-full items-center px-4 py-2 text-sm transition-colors duration-200',
                            ]"
                            @click="setTheme(themeOption.key as Theme)"
                        >
                            <Icon
                                :name="themeOption.icon"
                                class="mr-3 h-4 w-4"
                            />
                            {{ themeOption.label }}
                        </button>
                    </MenuItem>
                </div>
            </MenuItems>
        </transition>
    </Menu>
</template>
